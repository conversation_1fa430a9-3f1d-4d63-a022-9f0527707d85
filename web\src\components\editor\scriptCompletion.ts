import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
    description?: string;
    isVariadic?: boolean;
  }>;
  returnType?: string;
  outputType?: string;
  examples?: Array<{
    title: string;
    code: string;
    result?: string;
    description: string;
  }>;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 使用模板字符串生成类型定义 - 支持完整的输入输出参数、描述等
const generateTypeDefinitionsWithTemplate = (variables: VariableData[], interfaceName: string): string => {
  /**
   * 递归生成属性定义
   * @param vars 变量数组
   * @param indent 缩进字符串
   * @param parentPath 父级路径，用于生成嵌套接口名
   */
  const generateProperties = (vars: VariableData[], indent: string = '  ', parentPath: string = ''): string => {
    const properties: string[] = [];
    const nestedInterfaces: string[] = [];

    vars.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;

      // 生成注释 - 支持多行描述
      const comments: string[] = [];
      if (description) comments.push(description);
      if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
      if (type) comments.push(`类型: ${type}`);

      const commentBlock =
        comments.length > 0
          ? `${indent}/**\n${comments.map((c) => `${indent} * ${c}`).join('\n')}\n${indent} */\n`
          : '';

      // 处理嵌套对象
      if (children && children.length > 0) {
        const nestedInterfaceName = `${interfaceName}_${key}`;
        const nestedPath = parentPath ? `${parentPath}_${key}` : key;

        // 生成嵌套接口
        const nestedInterface = generateNestedInterface(children, nestedInterfaceName, nestedPath);
        nestedInterfaces.push(nestedInterface);

        // 添加属性引用
        properties.push(`${commentBlock}${indent}${key}: ${nestedInterfaceName};`);
      } else {
        // 普通属性
        const tsType = getTypeScriptType(type);
        properties.push(`${commentBlock}${indent}${key}: ${tsType};`);
      }
    });

    // 返回嵌套接口定义 + 属性定义
    const allInterfaces = nestedInterfaces.join('\n\n');
    const allProperties = properties.join('\n');

    return allInterfaces ? `${allInterfaces}\n\n${allProperties}` : allProperties;
  };

  /**
   * 生成嵌套接口
   */
  const generateNestedInterface = (vars: VariableData[], interfaceName: string, path: string): string => {
    const properties = generateProperties(vars, '  ', path);

    return `interface ${interfaceName} {
${properties}
}`;
  };

  // 生成主接口
  const properties = generateProperties(variables);

  return `interface ${interfaceName} {
${properties}
}`;
};

// 已移除旧的复杂类型定义生成方法，现在使用简化的模板字符串方法

// 使用模板字符串生成Utils类型定义 - 支持完整的参数描述
const generateUtilsTypeDefinition = (): string => {
  if (functions.length === 0) {
    return 'declare const Utils: any;';
  }

  const utilsMethods = functions
    .map((func) => {
      // 生成参数定义 - 使用模板字符串
      const generateParameters = (): string => {
        if (!func.parameters || func.parameters.length === 0) return '';

        return func.parameters
          .map((param) => {
            const paramType = getTypeScriptType(param.type);
            const optional = !param.required ? '?' : '';

            // 处理可变参数
            if (param.isVariadic) {
              return `...${param.name}: any[]`;
            } else {
              return `${param.name}${optional}: ${paramType}`;
            }
          })
          .join(', ');
      };

      // 生成返回类型
      const returnType = func.returnType ? getTypeScriptType(func.returnType) : 'any';

      // 生成完整的JSDoc注释 - 使用模板字符串
      const generateJSDoc = (): string => {
        const lines = ['  /**'];

        // 函数描述
        if (func.remark) {
          lines.push(`   * ${func.remark}`);
          if (func.label !== func.remark) {
            lines.push(`   * 中文名: ${func.label}`);
          }
        }

        // 参数说明
        if (func.parameters && func.parameters.length > 0) {
          lines.push('   *');
          func.parameters.forEach((param) => {
            const required = param.required ? '必需' : '可选';
            const defaultVal = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
            const desc = param.description || '';
            lines.push(`   * @param ${param.name} {${param.type}} ${required} - ${desc}${defaultVal}`);
          });
        }

        // 返回值说明
        if (func.returnType) {
          lines.push('   *');
          const outputDesc = func.outputType || func.returnType;
          lines.push(`   * @returns {${func.returnType}} ${outputDesc}`);
        }

        // 使用示例
        if (func.examples && func.examples.length > 0) {
          lines.push('   *');
          lines.push('   * @example');
          func.examples.slice(0, 2).forEach((example) => {
            lines.push(`   * // ${example.title}`);
            lines.push(`   * ${example.code}`);
            if (example.result) {
              lines.push(`   * // => ${example.result}`);
            }
            if (example.description) {
              lines.push(`   * // ${example.description}`);
            }
          });
        }

        lines.push('   */');
        return lines.join('\n');
      };

      const parameters = generateParameters();
      const jsdoc = generateJSDoc();

      return `${jsdoc}
  ${func.value}(${parameters}): ${returnType};`;
    })
    .join('\n\n');

  return `declare const Utils: {
${utilsMethods}
};`;
};

// 旧的JSDoc生成函数已被集成到Utils类型定义中

// 更新Monaco Editor的类型定义 - 使用模板字符串方法
const updateMonacoTypeDefinitions = () => {
  try {
    // 使用模板字符串生成类型定义，支持完整的描述和参数信息
    const localTypeDef =
      localVariables.length > 0 ? generateTypeDefinitionsWithTemplate(localVariables, 'LocalVariables') : '';
    const globalTypeDef =
      globalVariables.length > 0 ? generateTypeDefinitionsWithTemplate(globalVariables, 'GlobalVariables') : '';
    const currentTypeDef =
      currentVariables.length > 0 ? generateTypeDefinitionsWithTemplate(currentVariables, 'CurrentVariables') : '';

    // 生成_data对象的类型定义 - 正确处理局部和全局变量的嵌套结构
    let dataTypeDef = '';
    if (localVariables.length > 0 || globalVariables.length > 0) {
      // 生成_data对象的属性定义
      const generateDataObjectProperties = (): string => {
        const properties: string[] = [];

        // 处理局部变量
        localVariables.forEach((variable) => {
          const { key, type, description, pathDescription, children } = variable;

          // 生成注释
          const comments: string[] = [];
          if (description) comments.push(description);
          if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
          if (type) comments.push(`类型: ${type}`);

          const commentBlock =
            comments.length > 0 ? `  /**\n${comments.map((c) => `   * ${c}`).join('\n')}\n   */\n` : '';

          // 确定类型引用
          const tsType = children && children.length > 0 ? `LocalVariables_${key}` : getTypeScriptType(type);

          properties.push(`${commentBlock}  ${key}: ${tsType};`);
        });

        // 处理全局变量
        globalVariables.forEach((variable) => {
          const { key, type, description, pathDescription, children } = variable;

          // 生成注释
          const comments: string[] = [];
          if (description) comments.push(description);
          if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
          if (type) comments.push(`类型: ${type}`);

          const commentBlock =
            comments.length > 0 ? `  /**\n${comments.map((c) => `   * ${c}`).join('\n')}\n   */\n` : '';

          // 确定类型引用
          const tsType = children && children.length > 0 ? `GlobalVariables_${key}` : getTypeScriptType(type);

          properties.push(`${commentBlock}  ${key}: ${tsType};`);
        });

        return properties.join('\n');
      };

      const dataProperties = generateDataObjectProperties();
      dataTypeDef = `interface DataObject {\n${dataProperties}\n}`;
    }

    // 生成Utils类型定义（已使用模板字符串优化）
    const utilsTypeDef = generateUtilsTypeDefinition();

    // 生成当前变量的声明 - 使用模板字符串
    const currentVariableDeclarations = currentVariables
      .map((variable) => {
        const { key, type, description, pathDescription } = variable;

        // 生成注释
        const comments: string[] = [];
        if (description) comments.push(description);
        if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
        if (type) comments.push(`类型: ${type}`);

        const commentBlock = comments.length > 0 ? `/**\n * ${comments.join('\n * ')}\n */\n` : '';

        // 确定类型
        const tsType =
          variable.children && variable.children.length > 0 ? `CurrentVariables_${key}` : getTypeScriptType(type);

        return `${commentBlock}declare const ${key}: ${tsType};`;
      })
      .join('\n\n');

    // 组合所有类型定义
    const allTypeDefs = [
      localTypeDef,
      globalTypeDef,
      currentTypeDef,
      dataTypeDef,
      dataTypeDef ? 'declare const _data: DataObject;' : '',
      utilsTypeDef,
      currentVariableDeclarations,
    ]
      .filter((def) => def)
      .join('\n\n');

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 添加到Monaco Editor
    if (allTypeDefs) {
      monaco.languages.typescript.typescriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      monaco.languages.typescript.javascriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      console.log('已更新Monaco Editor类型定义:', allTypeDefs);
    }
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 获取TypeScript类型字符串
const getTypeScriptType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'string';
    case 'number':
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
    case 'bool':
      return 'boolean';
    case 'array':
      return 'any[]';
    default:
      return 'any';
  }
};

// 处理ROOT节点，展开其子节点（递归处理）
const expandRootNodes = (variables: VariableData[]): VariableData[] => {
  const result: VariableData[] = [];

  variables.forEach((variable) => {
    if (variable.key === 'ROOT' && variable.children && variable.children.length > 0) {
      // 如果是ROOT节点，递归展开其子节点
      const expandedChildren = expandRootNodes(variable.children);
      result.push(...expandedChildren);
    } else {
      // 非ROOT节点，递归处理其子节点
      const processedVariable = { ...variable };
      if (processedVariable.children && processedVariable.children.length > 0) {
        processedVariable.children = expandRootNodes(processedVariable.children);
      }
      result.push(processedVariable);
    }
  });

  return result;
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    // 处理ROOT节点展开
    currentVariables = expandRootNodes(data.currentVariables);
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    // 处理ROOT节点展开
    localVariables = expandRootNodes(data.localVariables);
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    // 处理ROOT节点展开
    globalVariables = expandRootNodes(data.globalVariables);
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
    console.log(
      '函数详细信息:',
      functions.map((f) => ({
        value: f.value,
        label: f.label,
        parameters: f.parameters,
        returnType: f.returnType,
        examples: f.examples?.length || 0,
      })),
    );
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 去重智能提示建议
const deduplicateSuggestions = (suggestions: any[]): any[] => {
  const seen = new Set<string>();
  return suggestions.filter((suggestion) => {
    const key = suggestion.label;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => {
    // 生成详细的文档说明
    const documentation = generateFunctionDocumentation(func);

    // 生成函数签名作为detail
    const signature = generateFunctionSignature(func);

    return {
      label: func.value, // 使用英文函数名而不是中文label
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: func.script,
      detail: signature, // 显示函数签名
      documentation: {
        value: documentation,
        isTrusted: true,
        supportHtml: true,
      },
      sortText: `1_${func.value}`,
    };
  });
};

// 生成函数签名
const generateFunctionSignature = (func: FunctionData): string => {
  let params = '';
  if (func.parameters && func.parameters.length > 0) {
    // 直接处理所有参数，包括可变参数
    params = func.parameters
      .map((param) => {
        const optional = !param.required ? '?' : '';
        // 如果是可变参数，使用 ...paramName: any[] 语法
        if (param.isVariadic) {
          return `...${param.name}: any[]`;
        } else {
          return `${param.name}${optional}: ${param.type}`;
        }
      })
      .join(', ');
  }

  const returnType = func.returnType || 'any';
  return `${func.value}(${params}): ${returnType}`;
};

// 生成函数文档
const generateFunctionDocumentation = (func: FunctionData): string => {
  const parts = [];

  // 添加中文名称和描述
  parts.push(`**${func.label}** - ${func.remark}`);

  // 添加函数签名
  const signature = generateFunctionSignature(func);
  parts.push(`\`\`\`typescript\n${signature}\n\`\`\``);

  // 添加参数说明
  if (func.parameters && func.parameters.length > 0) {
    parts.push('**参数说明:**');
    func.parameters.forEach((param) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      parts.push(`- \`${param.name}\` (${param.type}, ${required}): ${param.description || ''}${defaultValue}`);
    });
  }

  // 添加返回值说明
  if (func.returnType) {
    parts.push(`**返回值:** ${func.outputType || func.returnType}`);
  }

  // 添加使用示例
  if (func.examples && func.examples.length > 0) {
    parts.push('**使用示例:**');
    func.examples.slice(0, 3).forEach((example, index) => {
      parts.push(`${index + 1}. **${example.title}**`);
      parts.push(`\`\`\`javascript\n${example.code}\n\`\`\``);
      if (example.result) {
        parts.push(`结果: \`${example.result}\``);
      }
      if (example.description) {
        parts.push(`说明: ${example.description}`);
      }
      parts.push('');
    });
  }

  return parts.join('\n\n');
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts });

  for (const variable of variables) {
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable.key, '子属性数量:', variable.children?.length || 0);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  return null;
};

// 获取光标位置前的最后一个标识符上下文
const getLastIdentifierContext = (text: string): string => {
  // 从后往前查找最后一个完整的标识符上下文
  // 匹配模式：标识符. 在字符串末尾
  const match = text.match(/(\w+\.)$/);
  if (match) {
    return match[1];
  }

  // 如果末尾没有直接匹配，查找最后一个标识符.模式
  const allMatches = text.match(/(\w+\.)/g);
  if (allMatches && allMatches.length > 0) {
    return allMatches[allMatches.length - 1];
  }

  return '';
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 如果是通过点号触发的，需要精确识别当前上下文
  if (triggerCharacter === '.') {
    // 获取光标位置前的最后一个标识符上下文
    const lastContext = getLastIdentifierContext(trimmedText);

    // 只对Utils提供自定义建议，其他情况依赖TypeScript类型定义
    if (lastContext === 'Utils.') {
      return true;
    }

    // 对于临时变量、_data等，完全依赖 TypeScript 类型定义，不提供自定义建议
    // 这样可以避免重复显示，让TypeScript原生智能提示处理所有属性访问
    return false;
  }

  // 如果不是点号触发，只在特定情况下提供自定义建议
  // 检查是否是在输入自定义变量名或内置对象
  const lastWord = trimmedText.split(/\s+/).pop() || '';
  const isTypingCustomVariable =
    currentVariables.some((v) => v.key.startsWith(lastWord)) ||
    lastWord.includes('_data') ||
    lastWord.includes('Utils');

  return isTypingCustomVariable;
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    console.log('智能提示触发:', {
      trimmedText,
      triggerCharacter,
      shouldProvideCustom: shouldProvideCustomSuggestions(trimmedText, triggerCharacter),
    });

    // 只在特定情况下提供自定义建议，避免与默认 TypeScript 提示重复
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 如果是点号触发，需要精确识别当前上下文
      if (triggerCharacter === '.') {
        const lastContext = getLastIdentifierContext(trimmedText);

        // 如果是在 Utils. 后面，提供函数建议
        if (lastContext === 'Utils.') {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 其他点号触发的情况不提供自定义建议，依赖TypeScript类型定义
      }
      // 非点号触发时提供自定义变量建议
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getFirstLevelSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }

      // 去重处理，避免重复显示
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: uniqueSuggestions.length === 0, // 如果没有自定义建议，允许默认提示
      };
    }

    // 对于其他情况，返回空建议，让默认的 TypeScript 智能提示处理
    return {
      suggestions: [],
      incomplete: true, // 允许默认提示继续工作
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 如果是点号触发，需要精确识别当前上下文
      if (triggerCharacter === '.') {
        const lastContext = getLastIdentifierContext(trimmedText);

        // 如果是在 Utils. 后面，提供函数建议
        if (lastContext === 'Utils.') {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 其他点号触发的情况不提供自定义建议，依赖JavaScript类型定义
      }
      // 非点号触发时提供自定义变量建议
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getFirstLevelSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }

      // 去重处理，避免重复显示
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: uniqueSuggestions.length === 0, // 如果没有自定义建议，允许默认提示
      };
    }

    // 对于其他情况，返回空建议，让默认的 JavaScript 智能提示处理
    return {
      suggestions: [],
      incomplete: true, // 允许默认提示继续工作
    };
  },
});

// 提供Utils函数的参数提示
const provideUtilsSignatureHelp = (model: any, position: any) => {
  const { lineNumber, column } = position;

  // 获取当前行的文本
  const lineText = model.getLineContent(lineNumber);
  const textBeforePointer = lineText.substring(0, column - 1);

  // 查找Utils函数调用
  const utilsCallMatch = textBeforePointer.match(/Utils\.(\w+)\s*\(/);
  if (!utilsCallMatch) {
    return null;
  }

  const functionName = utilsCallMatch[1];
  const func = functions.find((f) => f.value === functionName);

  if (!func || !func.parameters || func.parameters.length === 0) {
    return null;
  }

  // 计算当前参数位置
  const functionCallStart = utilsCallMatch.index! + utilsCallMatch[0].length - 1; // 括号位置
  const currentParamText = textBeforePointer.substring(functionCallStart + 1);
  const commaCount = (currentParamText.match(/,/g) || []).length;

  // 检查是否有可变长度参数
  const hasVariadicParam = func.parameters.some((param) => param.isVariadic);
  const activeParameter = hasVariadicParam ? 0 : Math.min(commaCount, func.parameters.length - 1);

  // 生成参数信息
  const parameters = func.parameters.map((param) => {
    const isOptional = !param.required;
    const paramType = param.type === 'DateTime' ? 'Date' : param.type;

    let label: string;
    let documentation: string;

    if (param.isVariadic) {
      label = `...${param.name}: any[]`;
      documentation = param.description || `可变长度参数 ${param.name} - 可以传入任意数量的参数`;
    } else {
      label = `${param.name}${isOptional ? '?' : ''}: ${paramType}`;
      documentation = param.description || `参数 ${param.name}`;
    }

    return {
      label: label,
      documentation: {
        value: documentation,
        isTrusted: true,
      },
    };
  });

  // 生成函数签名
  const signature = {
    label: generateFunctionSignature(func),
    documentation: {
      value: `**${func.label}** - ${func.remark}`,
      isTrusted: true,
    },
    parameters: parameters,
    activeParameter: activeParameter,
  };

  return {
    value: {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: activeParameter,
    },
    dispose: () => {},
  };
};

// 注册参数提示提供者 - TypeScript
monaco.languages.registerSignatureHelpProvider('typescript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});

// 注册参数提示提供者 - JavaScript
monaco.languages.registerSignatureHelpProvider('javascript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});
