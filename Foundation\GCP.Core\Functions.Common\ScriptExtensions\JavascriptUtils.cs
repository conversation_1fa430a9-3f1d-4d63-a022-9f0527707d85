﻿using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Attributes;
using Jint;
using Jint.Native.Json;
using System.Collections;
using System.Linq;

namespace GCP.Functions.Common.ScriptExtensions
{
    public class JavascriptUtils
    {
        private DbContext db { get; set; }

        public JavascriptUtils(DbContext db = null)
        {
            this.db = db;
        }

        [UtilsFunction("随机ID", "生成随机的UUID字符串", "Common", "常用函数", "string")]
        [FunctionExample("基本用法", "Utils.UUID()", "\"abc123def456\"", "生成一个随机的UUID字符串")]
        [FunctionExample("在变量中使用", "var id = Utils.UUID(); console.log(id);", "\"abc123def456\"", "将生成的UUID保存到变量中")]
        public string UUID() => TUID.NewTUID().ToString();

        [UtilsFunction("当前时间", "获取当前日期和时间", "Common", "常用函数", "DateTime")]
        [FunctionExample("基本用法", "Utils.NOW()", "2024-01-15 14:30:25", "获取当前的日期和时间")]
        [FunctionExample("格式化当前时间", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy-MM-dd\")", "\"2024-01-15\"", "获取当前日期并格式化为字符串")]
        public DateTime NOW() => DateTime.Now;

        [UtilsFunction("条件判断", "根据条件返回不同的值", "Common", "常用函数", "object")]
        [FunctionExample("基本条件判断", "Utils.IF(true, \"是\", \"否\")", "\"是\"", "当条件为真时返回第一个值")]
        [FunctionExample("数值比较", "Utils.IF(age >= 18, \"成年人\", \"未成年人\")", "\"成年人\"", "根据年龄判断是否成年")]
        [FunctionExample("嵌套使用", "Utils.IF(score >= 90, \"优秀\", Utils.IF(score >= 60, \"及格\", \"不及格\"))", "\"优秀\"", "多层条件判断")]
        public object IF([ParameterDescription("条件表达式")] bool condition,
                        [ParameterDescription("条件为真时的值")] object trueValue,
                        [ParameterDescription("条件为假时的值")] object falseValue)
        {
            return condition ? trueValue : falseValue;
        }

        [UtilsFunction("时间转文本", "将时间转换为指定格式的字符串", "Common", "常用函数", "string")]
        [FunctionExample("默认格式", "Utils.DATE_TO_STRING(Utils.NOW())", "\"2024-01-15 14:30:25\"", "使用默认格式转换时间")]
        [FunctionExample("日期格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy-MM-dd\")", "\"2024-01-15\"", "只显示日期部分")]
        [FunctionExample("时间格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"HH:mm:ss\")", "\"14:30:25\"", "只显示时间部分")]
        [FunctionExample("中文格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy年MM月dd日\")", "\"2024年01月15日\"", "使用中文日期格式")]
        public object DATE_TO_STRING([ParameterDescription("要转换的时间")] DateTime time,
                                    [ParameterDescription("时间格式")] string format = "yyyy-MM-dd HH:mm:ss")
        {
            return time.ToLocalTime().ToString(format);
        }

        private static readonly Engine _engine;

        static JavascriptUtils()
        {
            _engine = new Engine();
            _engine.Execute("""

                            function groupByMultipleFieldsDynamic(array, groupFields, fields, childField = 'items') {
                              if (!array || array.length === 0) return [];
                              if (!fields || fields.length === 0) {
                                fields = Object.keys(array[0]).filter((field) => !groupFields.includes(field));
                              }

                              return array.reduce((result, item) => {
                                const group = result.find((g) => groupFields.every((field) => item[field] === g[field]));

                                const extra = {};
                                fields.forEach((field) => {
                                  extra[field] = item[field];
                                });

                                if (group) {
                                  group[childField].push(extra);
                                } else {
                                  const dataItem = { [childField]: [] };
                                  groupFields.forEach((field) => {
                                    dataItem[field] = item[field];
                                  });
                                  dataItem[childField].push(extra);

                                  result.push(dataItem);
                                }
                                return result;
                              }, []);
                            }

                            """);
            _engine.Execute("""

                            function jsonParse(jsonString) {
                              return JSON.parse(jsonString);
                            }

                            """);
        }

        [UtilsFunction("动态字段分组", "根据动态字段对数组进行分组", "Array", "数组操作", "array")]
        [FunctionExample("基本分组", "Utils.GROUP_BY_DYNAMIC_FIELD([{name:'张三',age:25,city:'北京'},{name:'李四',age:30,city:'北京'},{name:'王五',age:28,city:'上海'}], ['city'], ['name','age'])", "[{city:'北京',body:[{name:'张三',age:25},{name:'李四',age:30}]},{city:'上海',body:[{name:'王五',age:28}]}]", "按城市分组用户信息")]
        [FunctionExample("多字段分组", "Utils.GROUP_BY_DYNAMIC_FIELD(orders, ['status','priority'], ['orderId','amount'], 'items')", "[{status:'pending',priority:'high',items:[{orderId:1,amount:100}]}]", "按状态和优先级分组订单")]
        [FunctionExample("自定义子字段", "Utils.GROUP_BY_DYNAMIC_FIELD(products, ['category'], ['name','price'], 'products')", "[{category:'电子产品',products:[{name:'手机',price:3000}]}]", "按分类分组产品，子字段名为products")]
        public object GROUP_BY_DYNAMIC_FIELD([ParameterDescription("要分组的数组")] object array,
                                           [ParameterDescription("分组字段数组")] string[] groupFields,
                                           [ParameterDescription("其他字段数组")] string[] fields,
                                           [ParameterDescription("子节点字段名")] string childField = "body")
        {
            return _engine.Invoke("groupByMultipleFieldsDynamic", array, groupFields, fields, childField);
        }

        [UtilsFunction("JSON解析", "解析JSON字符串为对象", "Common", "常用函数", "object")]
        [FunctionExample("解析对象", "Utils.JSON_PARSE('{\"name\":\"张三\",\"age\":25}')", "{name: \"张三\", age: 25}", "解析JSON字符串为对象")]
        [FunctionExample("解析数组", "Utils.JSON_PARSE('[1,2,3,4,5]')", "[1, 2, 3, 4, 5]", "解析JSON数组")]
        [FunctionExample("访问属性", "Utils.JSON_PARSE('{\"user\":{\"name\":\"李四\"}}').user.name", "\"李四\"", "解析后访问嵌套属性")]
        public object JSON_PARSE([ParameterDescription("JSON字符串")] string jsonString)
        {
            var parser = new JsonParser(_engine);
            return parser.Parse(jsonString).ToObject();
            //return (_engine.Invoke("jsonParse", jsonString) as JsObject).ToObject();
        }

        #region 字符串处理函数
        /// <summary>
        /// 字符串拼接
        /// </summary>
        [UtilsFunction("字符串拼接", "将多个值拼接成字符串", "String", "字符串处理", "string")]
        [FunctionExample("基本拼接", "Utils.STRING_CONCAT(\"Hello\", \" \", \"World\")", "\"Hello World\"", "拼接多个字符串")]
        [FunctionExample("数字拼接", "Utils.STRING_CONCAT(\"订单号:\", 12345)", "\"订单号:12345\"", "拼接字符串和数字")]
        [FunctionExample("变量拼接", "Utils.STRING_CONCAT(name, \"的年龄是\", age, \"岁\")", "\"张三的年龄是25岁\"", "拼接变量和文本")]
        public string STRING_CONCAT([ParameterDescription("要拼接的值数组")] params object[] values)
        {
            return string.Concat(values?.Select(v => v?.ToString() ?? "") ?? new string[0]);
        }

        /// <summary>
        /// 字符串截取
        /// </summary>
        [UtilsFunction("字符串截取", "截取字符串的指定部分", "String", "字符串处理", "string")]
        [FunctionExample("从指定位置截取", "Utils.STRING_SUBSTRING(\"Hello World\", 6)", "\"World\"", "从位置6开始截取到末尾")]
        [FunctionExample("截取指定长度", "Utils.STRING_SUBSTRING(\"Hello World\", 0, 5)", "\"Hello\"", "从开始位置截取5个字符")]
        [FunctionExample("中间截取", "Utils.STRING_SUBSTRING(\"JavaScript\", 4, 6)", "\"Script\"", "从位置4开始截取6个字符")]
        public string STRING_SUBSTRING([ParameterDescription("输入字符串")] string input,
                                     [ParameterDescription("开始位置")] int startIndex,
                                     [ParameterDescription("截取长度")] int? length = null)
        {
            if (string.IsNullOrEmpty(input) || startIndex < 0 || startIndex >= input.Length)
                return "";

            if (length.HasValue)
            {
                var actualLength = Math.Min(length.Value, input.Length - startIndex);
                return actualLength > 0 ? input.Substring(startIndex, actualLength) : "";
            }
            return input.Substring(startIndex);
        }

        /// <summary>
        /// 字符串替换
        /// </summary>
        [UtilsFunction("字符串替换", "替换字符串中的指定内容", "String", "字符串处理", "string")]
        [FunctionExample("基本替换", "Utils.STRING_REPLACE(\"Hello World\", \"World\", \"JavaScript\")", "\"Hello JavaScript\"", "将World替换为JavaScript")]
        [FunctionExample("多次替换", "Utils.STRING_REPLACE(\"apple,apple,banana\", \"apple\", \"orange\")", "\"orange,orange,banana\"", "替换所有匹配的内容")]
        [FunctionExample("空字符替换", "Utils.STRING_REPLACE(\"a b c\", \" \", \"\")", "\"abc\"", "移除所有空格")]
        public string STRING_REPLACE([ParameterDescription("输入字符串")] string input,
                                   [ParameterDescription("要替换的内容")] string oldValue,
                                   [ParameterDescription("新内容")] string newValue)
        {
            return input?.Replace(oldValue ?? "", newValue ?? "") ?? "";
        }

        /// <summary>
        /// 去除空格
        /// </summary>
        [UtilsFunction("去除空格", "去除字符串首尾空格", "String", "字符串处理", "string")]
        [FunctionExample("去除首尾空格", "Utils.STRING_TRIM(\"  Hello World  \")", "\"Hello World\"", "去除字符串首尾的空格")]
        [FunctionExample("只有空格的字符串", "Utils.STRING_TRIM(\"   \")", "\"\"", "全是空格的字符串返回空字符串")]
        [FunctionExample("无空格字符串", "Utils.STRING_TRIM(\"Hello\")", "\"Hello\"", "没有空格的字符串保持不变")]
        public string STRING_TRIM([ParameterDescription("输入字符串")] string input)
        {
            return input?.Trim() ?? "";
        }

        /// <summary>
        /// 转换为大写
        /// </summary>
        [UtilsFunction("转大写", "将字符串转换为大写", "String", "字符串处理", "string")]
        [FunctionExample("基本转换", "Utils.STRING_UPPER(\"hello world\")", "\"HELLO WORLD\"", "将字符串转换为大写")]
        [FunctionExample("混合大小写", "Utils.STRING_UPPER(\"Hello World\")", "\"HELLO WORLD\"", "混合大小写转换为大写")]
        [FunctionExample("包含数字", "Utils.STRING_UPPER(\"abc123def\")", "\"ABC123DEF\"", "字母转大写，数字保持不变")]
        public string STRING_UPPER([ParameterDescription("输入字符串")] string input)
        {
            return input?.ToUpper() ?? "";
        }

        /// <summary>
        /// 转换为小写
        /// </summary>
        [UtilsFunction("转小写", "将字符串转换为小写", "String", "字符串处理", "string")]
        [FunctionExample("基本转换", "Utils.STRING_LOWER(\"HELLO WORLD\")", "\"hello world\"", "将字符串转换为小写")]
        [FunctionExample("混合大小写", "Utils.STRING_LOWER(\"Hello World\")", "\"hello world\"", "混合大小写转换为小写")]
        [FunctionExample("包含数字", "Utils.STRING_LOWER(\"ABC123DEF\")", "\"abc123def\"", "字母转小写，数字保持不变")]
        public string STRING_LOWER([ParameterDescription("输入字符串")] string input)
        {
            return input?.ToLower() ?? "";
        }

        /// <summary>
        /// 字符串分割
        /// </summary>
        [UtilsFunction("字符串分割", "按分隔符分割字符串", "String", "字符串处理", "array")]
        [FunctionExample("逗号分割", "Utils.STRING_SPLIT(\"apple,banana,orange\", \",\")", "[\"apple\", \"banana\", \"orange\"]", "按逗号分割字符串")]
        [FunctionExample("空格分割", "Utils.STRING_SPLIT(\"Hello World JavaScript\", \" \")", "[\"Hello\", \"World\", \"JavaScript\"]", "按空格分割字符串")]
        [FunctionExample("自定义分隔符", "Utils.STRING_SPLIT(\"a|b|c|d\", \"|\")", "[\"a\", \"b\", \"c\", \"d\"]", "按自定义分隔符分割")]
        public string[] STRING_SPLIT([ParameterDescription("输入字符串")] string input,
                                   [ParameterDescription("分隔符")] string separator)
        {
            if (string.IsNullOrEmpty(input)) return new string[0];
            return input.Split(new[] { separator }, StringSplitOptions.None);
        }

        /// <summary>
        /// 字符串长度
        /// </summary>
        [UtilsFunction("字符串长度", "获取字符串长度", "String", "字符串处理", "int")]
        [FunctionExample("基本用法", "Utils.STRING_LENGTH(\"Hello World\")", "11", "获取字符串的长度")]
        [FunctionExample("空字符串", "Utils.STRING_LENGTH(\"\")", "0", "空字符串的长度为0")]
        [FunctionExample("中文字符", "Utils.STRING_LENGTH(\"你好世界\")", "4", "中文字符串的长度")]
        public int STRING_LENGTH([ParameterDescription("输入字符串")] string input)
        {
            return input?.Length ?? 0;
        }

        /// <summary>
        /// 字符串是否包含
        /// </summary>
        [UtilsFunction("包含检查", "检查字符串是否包含指定内容", "String", "字符串处理", "bool")]
        [FunctionExample("基本检查", "Utils.STRING_CONTAINS(\"Hello World\", \"World\")", "true", "检查字符串是否包含指定内容")]
        [FunctionExample("不包含", "Utils.STRING_CONTAINS(\"Hello World\", \"JavaScript\")", "false", "不包含指定内容返回false")]
        [FunctionExample("大小写敏感", "Utils.STRING_CONTAINS(\"Hello World\", \"world\")", "false", "大小写敏感的包含检查")]
        public bool STRING_CONTAINS([ParameterDescription("输入字符串")] string input,
                                  [ParameterDescription("要检查的内容")] string value)
        {
            return input?.Contains(value ?? "") ?? false;
        }

        /// <summary>
        /// 字符串开始于
        /// </summary>
        [UtilsFunction("开始检查", "检查字符串是否以指定内容开始", "String", "字符串处理", "bool")]
        [FunctionExample("基本检查", "Utils.STRING_STARTS_WITH(\"Hello World\", \"Hello\")", "true", "检查字符串是否以指定内容开始")]
        [FunctionExample("不匹配", "Utils.STRING_STARTS_WITH(\"Hello World\", \"World\")", "false", "不以指定内容开始返回false")]
        [FunctionExample("空字符串检查", "Utils.STRING_STARTS_WITH(\"Hello\", \"\")", "true", "任何字符串都以空字符串开始")]
        public bool STRING_STARTS_WITH([ParameterDescription("输入字符串")] string input,
                                     [ParameterDescription("要检查的开始内容")] string value)
        {
            return input?.StartsWith(value ?? "") ?? false;
        }

        /// <summary>
        /// 字符串结束于
        /// </summary>
        [UtilsFunction("结束检查", "检查字符串是否以指定内容结束", "String", "字符串处理", "bool")]
        [FunctionExample("基本检查", "Utils.STRING_ENDS_WITH(\"Hello World\", \"World\")", "true", "检查字符串是否以指定内容结束")]
        [FunctionExample("不匹配", "Utils.STRING_ENDS_WITH(\"Hello World\", \"Hello\")", "false", "不以指定内容结束返回false")]
        [FunctionExample("文件扩展名检查", "Utils.STRING_ENDS_WITH(\"document.pdf\", \".pdf\")", "true", "检查文件是否为PDF格式")]
        public bool STRING_ENDS_WITH([ParameterDescription("输入字符串")] string input,
                                   [ParameterDescription("要检查的结束内容")] string value)
        {
            return input?.EndsWith(value ?? "") ?? false;
        }
        #endregion

        #region 数组处理函数
        /// <summary>
        /// 数组长度
        /// </summary>
        [UtilsFunction("数组长度", "获取数组或集合的长度", "Array", "数组操作", "int")]
        [FunctionExample("基本用法", "Utils.ARRAY_LENGTH([1, 2, 3, 4, 5])", "5", "获取数组的长度")]
        [FunctionExample("空数组", "Utils.ARRAY_LENGTH([])", "0", "空数组的长度为0")]
        [FunctionExample("字符串数组", "Utils.ARRAY_LENGTH([\"a\", \"b\", \"c\"])", "3", "获取字符串数组的长度")]
        public int ARRAY_LENGTH([ParameterDescription("输入数组")] object array)
        {
            if (array is Array arr) return arr.Length;
            if (array is System.Collections.ICollection collection) return collection.Count;
            return 0;
        }

        /// <summary>
        /// 数组是否包含元素
        /// </summary>
        [UtilsFunction("包含检查", "检查数组是否包含指定元素", "Array", "数组操作", "bool")]
        [FunctionExample("数字检查", "Utils.ARRAY_CONTAINS([1, 2, 3, 4, 5], 3)", "true", "检查数组是否包含数字3")]
        [FunctionExample("字符串检查", "Utils.ARRAY_CONTAINS([\"apple\", \"banana\", \"orange\"], \"banana\")", "true", "检查数组是否包含指定字符串")]
        [FunctionExample("不存在的元素", "Utils.ARRAY_CONTAINS([1, 2, 3], 5)", "false", "检查不存在的元素返回false")]
        public bool ARRAY_CONTAINS([ParameterDescription("输入数组")] object array,
                                  [ParameterDescription("要检查的值")] object value)
        {
            if (array is Array arr)
            {
                return arr.Cast<object>().Contains(value);
            }
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().Contains(value);
            }
            return false;
        }

        /// <summary>
        /// 获取数组第一个元素
        /// </summary>
        [UtilsFunction("第一个元素", "获取数组第一个元素", "Array", "数组操作", "object")]
        [FunctionExample("数字数组", "Utils.ARRAY_FIRST([1, 2, 3, 4, 5])", "1", "获取数字数组的第一个元素")]
        [FunctionExample("字符串数组", "Utils.ARRAY_FIRST([\"apple\", \"banana\", \"orange\"])", "\"apple\"", "获取字符串数组的第一个元素")]
        [FunctionExample("空数组", "Utils.ARRAY_FIRST([])", "null", "空数组返回null")]
        public object ARRAY_FIRST([ParameterDescription("输入数组")] object array)
        {
            if (array is Array arr && arr.Length > 0) return arr.GetValue(0);
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 获取数组最后一个元素
        /// </summary>
        [UtilsFunction("最后一个元素", "获取数组最后一个元素", "Array", "数组操作", "object")]
        [FunctionExample("数字数组", "Utils.ARRAY_LAST([1, 2, 3, 4, 5])", "5", "获取数字数组的最后一个元素")]
        [FunctionExample("字符串数组", "Utils.ARRAY_LAST([\"apple\", \"banana\", \"orange\"])", "\"orange\"", "获取字符串数组的最后一个元素")]
        [FunctionExample("单元素数组", "Utils.ARRAY_LAST([\"only\"])", "\"only\"", "单元素数组返回该元素")]
        public object ARRAY_LAST([ParameterDescription("输入数组")] object array)
        {
            if (array is Array arr && arr.Length > 0) return arr.GetValue(arr.Length - 1);
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().LastOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 数组连接为字符串
        /// </summary>
        [UtilsFunction("数组连接", "将数组元素连接为字符串", "Array", "数组操作", "string")]
        [FunctionExample("默认分隔符", "Utils.ARRAY_JOIN([1, 2, 3, 4, 5])", "\"1,2,3,4,5\"", "使用默认逗号分隔符连接数组")]
        [FunctionExample("自定义分隔符", "Utils.ARRAY_JOIN([\"apple\", \"banana\", \"orange\"], \" | \")", "\"apple | banana | orange\"", "使用自定义分隔符连接数组")]
        [FunctionExample("空数组", "Utils.ARRAY_JOIN([], \"-\")", "\"\"", "空数组返回空字符串")]
        public string ARRAY_JOIN([ParameterDescription("输入数组")] object array,
                               [ParameterDescription("分隔符")] string separator = ",")
        {
            if (array is Array arr)
            {
                return string.Join(separator, arr.Cast<object>().Select(x => x?.ToString() ?? ""));
            }
            if (array is System.Collections.IEnumerable enumerable)
            {
                return string.Join(separator, enumerable.Cast<object>().Select(x => x?.ToString() ?? ""));
            }
            return "";
        }
        #endregion

        #region 时间处理函数
        /// <summary>
        /// 日期加减
        /// </summary>
        [UtilsFunction("日期加减", "对日期进行加减运算", "DateTime", "日期时间", "DateTime")]
        [FunctionExample("加天数", "Utils.DATE_ADD(Utils.NOW(), 7, \"days\")", "2024-01-22 14:30:25", "当前时间加7天")]
        [FunctionExample("减月数", "Utils.DATE_ADD(Utils.NOW(), -1, \"months\")", "2023-12-15 14:30:25", "当前时间减1个月")]
        [FunctionExample("加小时", "Utils.DATE_ADD(Utils.NOW(), 2, \"hours\")", "2024-01-15 16:30:25", "当前时间加2小时")]
        public DateTime DATE_ADD([ParameterDescription("基准日期")] DateTime date,
                               [ParameterDescription("加减数值")] int value,
                               [ParameterDescription("时间单位")] string unit = "days")
        {
            return unit.ToLower() switch
            {
                "years" => date.AddYears(value),
                "months" => date.AddMonths(value),
                "days" => date.AddDays(value),
                "hours" => date.AddHours(value),
                "minutes" => date.AddMinutes(value),
                "seconds" => date.AddSeconds(value),
                _ => date.AddDays(value)
            };
        }

        /// <summary>
        /// 日期差值
        /// </summary>
        [UtilsFunction("日期差值", "计算两个日期的差值", "DateTime", "日期时间", "int")]
        [FunctionExample("天数差值", "Utils.DATE_DIFF(Utils.DATE_PARSE(\"2024-01-20\"), Utils.DATE_PARSE(\"2024-01-15\"), \"days\")", "5", "计算两个日期相差的天数")]
        [FunctionExample("小时差值", "Utils.DATE_DIFF(Utils.DATE_PARSE(\"2024-01-15 16:00:00\"), Utils.DATE_PARSE(\"2024-01-15 14:00:00\"), \"hours\")", "2", "计算两个时间相差的小时数")]
        [FunctionExample("年份差值", "Utils.DATE_DIFF(Utils.DATE_PARSE(\"2024-01-15\"), Utils.DATE_PARSE(\"2020-01-15\"), \"years\")", "4", "计算两个日期相差的年数")]
        public double DATE_DIFF([ParameterDescription("日期1")] DateTime date1,
                              [ParameterDescription("日期2")] DateTime date2,
                              [ParameterDescription("时间单位")] string unit = "days")
        {
            var timeSpan = date1 - date2;
            return unit.ToLower() switch
            {
                "years" => timeSpan.TotalDays / 365.25,
                "months" => timeSpan.TotalDays / 30.44,
                "days" => timeSpan.TotalDays,
                "hours" => timeSpan.TotalHours,
                "minutes" => timeSpan.TotalMinutes,
                "seconds" => timeSpan.TotalSeconds,
                _ => timeSpan.TotalDays
            };
        }

        /// <summary>
        /// 日期格式化
        /// </summary>
        [UtilsFunction("日期格式化", "格式化日期为字符串", "DateTime", "日期时间", "string")]
        [FunctionExample("默认格式", "Utils.DATE_FORMAT(Utils.NOW())", "\"2024-01-15 14:30:25\"", "使用默认格式格式化日期")]
        [FunctionExample("仅日期", "Utils.DATE_FORMAT(Utils.NOW(), \"yyyy-MM-dd\")", "\"2024-01-15\"", "只显示日期部分")]
        [FunctionExample("中文格式", "Utils.DATE_FORMAT(Utils.NOW(), \"yyyy年MM月dd日 HH时mm分\")", "\"2024年01月15日 14时30分\"", "使用中文格式")]
        public string DATE_FORMAT([ParameterDescription("要格式化的日期")] DateTime date,
                                [ParameterDescription("格式字符串")] string format = "yyyy-MM-dd HH:mm:ss")
        {
            return date.ToString(format);
        }

        /// <summary>
        /// 日期解析
        /// </summary>
        [UtilsFunction("日期解析", "解析字符串为日期", "DateTime", "日期时间", "DateTime")]
        [FunctionExample("标准格式", "Utils.DATE_PARSE(\"2024-01-15 14:30:25\")", "2024-01-15 14:30:25", "解析标准日期时间格式")]
        [FunctionExample("仅日期", "Utils.DATE_PARSE(\"2024-01-15\")", "2024-01-15 00:00:00", "解析仅日期格式")]
        [FunctionExample("无效格式", "Utils.DATE_PARSE(\"invalid date\")", "null", "无效日期字符串返回null")]
        public DateTime? DATE_PARSE([ParameterDescription("日期字符串")] string dateString)
        {
            if (DateTime.TryParse(dateString, out var result))
                return result;
            return null;
        }

        /// <summary>
        /// 获取当前时间戳
        /// </summary>
        [UtilsFunction("时间戳", "获取日期的时间戳", "DateTime", "日期时间", "int")]
        [FunctionExample("当前时间戳", "Utils.DATE_TIMESTAMP()", "1705312225", "获取当前时间的Unix时间戳")]
        [FunctionExample("指定日期时间戳", "Utils.DATE_TIMESTAMP(Utils.DATE_PARSE(\"2024-01-15 14:30:25\"))", "1705312225", "获取指定日期的时间戳")]
        [FunctionExample("空参数", "Utils.DATE_TIMESTAMP(null)", "1705312225", "空参数时使用当前时间")]
        public long DATE_TIMESTAMP([ParameterDescription("日期时间")] DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Now;
            return ((DateTimeOffset)targetDate).ToUnixTimeSeconds();
        }

        /// <summary>
        /// 从时间戳获取日期
        /// </summary>
        [UtilsFunction("时间戳转日期", "从时间戳获取日期", "DateTime", "日期时间", "DateTime")]
        [FunctionExample("基本转换", "Utils.DATE_FROM_TIMESTAMP(1705312225)", "2024-01-15 14:30:25", "将Unix时间戳转换为日期时间")]
        [FunctionExample("历史时间", "Utils.DATE_FROM_TIMESTAMP(0)", "1970-01-01 08:00:00", "Unix纪元时间")]
        [FunctionExample("未来时间", "Utils.DATE_FROM_TIMESTAMP(2000000000)", "2033-05-18 11:33:20", "未来时间戳转换")]
        public DateTime DATE_FROM_TIMESTAMP([ParameterDescription("Unix时间戳")] long timestamp)
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
        #endregion

        #region 字典操作函数
        /// <summary>
        /// 获取字典值
        /// </summary>
        [UtilsFunction("获取字典值", "从字典中获取指定键的值", "Dictionary", "字典操作", "object")]
        [FunctionExample("基本获取", "Utils.DICT_GET({name: \"张三\", age: 25}, \"name\")", "\"张三\"", "从字典中获取name字段的值")]
        [FunctionExample("不存在的键", "Utils.DICT_GET({name: \"张三\"}, \"age\")", "null", "获取不存在的键返回null")]
        [FunctionExample("默认值", "Utils.DICT_GET({name: \"张三\"}, \"age\", 18)", "18", "获取不存在的键时返回默认值")]
        public object DICT_GET([ParameterDescription("字典对象")] object dict,
                             [ParameterDescription("键名")] string key,
                             [ParameterDescription("默认值")] object defaultValue = null)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Contains(key) ? iDict[key] : defaultValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 设置字典值
        /// </summary>
        [UtilsFunction("设置字典值", "设置字典的键值对", "Dictionary", "字典操作", "object")]
        [FunctionExample("新增键值", "Utils.DICT_SET({name: \"张三\"}, \"age\", 25)", "{name: \"张三\", age: 25}", "向字典添加新的键值对")]
        [FunctionExample("更新值", "Utils.DICT_SET({name: \"张三\", age: 25}, \"age\", 26)", "{name: \"张三\", age: 26}", "更新字典中已存在的键的值")]
        [FunctionExample("空字典", "Utils.DICT_SET({}, \"name\", \"李四\")", "{name: \"李四\"}", "向空字典添加键值对")]
        public Dictionary<string, object> DICT_SET([ParameterDescription("字典对象")] object dict,
                                                 [ParameterDescription("键名")] string key,
                                                 [ParameterDescription("值")] object value)
        {
            Dictionary<string, object> result;
            if (dict is Dictionary<string, object> dictionary)
            {
                result = new Dictionary<string, object>(dictionary);
            }
            else if (dict is IDictionary<string, object> iDict)
            {
                result = new Dictionary<string, object>(iDict);
            }
            else
            {
                result = new Dictionary<string, object>();
            }

            result[key] = value;
            return result;
        }

        /// <summary>
        /// 字典合并
        /// </summary>
        [UtilsFunction("字典合并", "合并多个字典", "Dictionary", "字典操作", "object")]
        [FunctionExample("两个字典", "Utils.DICT_MERGE({name: \"张三\"}, {age: 25})", "{name: \"张三\", age: 25}", "合并两个字典")]
        [FunctionExample("重复键覆盖", "Utils.DICT_MERGE({name: \"张三\", age: 25}, {age: 26, city: \"北京\"})", "{name: \"张三\", age: 26, city: \"北京\"}", "后面的字典会覆盖前面的同名键")]
        [FunctionExample("多个字典", "Utils.DICT_MERGE({a: 1}, {b: 2}, {c: 3})", "{a: 1, b: 2, c: 3}", "合并多个字典")]
        public Dictionary<string, object> DICT_MERGE([ParameterDescription("要合并的字典数组")] params object[] dicts)
        {
            var result = new Dictionary<string, object>();
            foreach (var dict in dicts)
            {
                if (dict is IDictionary<string, object> dictionary)
                {
                    foreach (var kvp in dictionary)
                    {
                        result[kvp.Key] = kvp.Value;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取字典所有键
        /// </summary>
        [UtilsFunction("获取所有键", "获取字典的所有键", "Dictionary", "字典操作", "array")]
        [FunctionExample("基本用法", "Utils.DICT_KEYS({name: \"张三\", age: 25, city: \"北京\"})", "[\"name\", \"age\", \"city\"]", "获取字典的所有键名")]
        [FunctionExample("空字典", "Utils.DICT_KEYS({})", "[]", "空字典返回空数组")]
        [FunctionExample("单键字典", "Utils.DICT_KEYS({id: 123})", "[\"id\"]", "单键字典返回包含一个元素的数组")]
        public string[] DICT_KEYS([ParameterDescription("字典对象")] object dict)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.Keys.ToArray();
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Keys.Cast<string>().ToArray();
            }
            return new string[0];
        }

        /// <summary>
        /// 获取字典所有值
        /// </summary>
        [UtilsFunction("获取所有值", "获取字典的所有值", "Dictionary", "字典操作", "array")]
        [FunctionExample("基本用法", "Utils.DICT_VALUES({name: \"张三\", age: 25, city: \"北京\"})", "[\"张三\", 25, \"北京\"]", "获取字典的所有值")]
        [FunctionExample("空字典", "Utils.DICT_VALUES({})", "[]", "空字典返回空数组")]
        [FunctionExample("混合类型值", "Utils.DICT_VALUES({str: \"text\", num: 123, bool: true})", "[\"text\", 123, true]", "获取不同类型的值")]
        public object[] DICT_VALUES([ParameterDescription("字典对象")] object dict)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.Values.ToArray();
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Values.Cast<object>().ToArray();
            }
            return new object[0];
        }

        /// <summary>
        /// 检查字典是否包含键
        /// </summary>
        [UtilsFunction("检查键存在", "检查字典是否包含指定键", "Dictionary", "字典操作", "bool")]
        [FunctionExample("存在的键", "Utils.DICT_HAS_KEY({name: \"张三\", age: 25}, \"name\")", "true", "检查字典是否包含指定键")]
        [FunctionExample("不存在的键", "Utils.DICT_HAS_KEY({name: \"张三\", age: 25}, \"city\")", "false", "不存在的键返回false")]
        [FunctionExample("空字典", "Utils.DICT_HAS_KEY({}, \"any\")", "false", "空字典不包含任何键")]
        public bool DICT_HAS_KEY([ParameterDescription("字典对象")] object dict,
                               [ParameterDescription("键名")] string key)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.ContainsKey(key);
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Contains(key);
            }
            return false;
        }
        #endregion

        #region 逻辑和数学函数

        /// <summary>
        /// 数值比较
        /// </summary>
        [UtilsFunction("数值比较", "比较两个数值", "Math", "数学运算", "bool")]
        [FunctionExample("大于比较", "Utils.COMPARE(10, \">\", 5)", "true", "比较10是否大于5")]
        [FunctionExample("等于比较", "Utils.COMPARE(5, \"==\", 5)", "true", "比较两个数是否相等")]
        [FunctionExample("小于等于", "Utils.COMPARE(3, \"<=\", 5)", "true", "比较3是否小于等于5")]
        public bool COMPARE([ParameterDescription("第一个数值")] object value1,
                          [ParameterDescription("比较操作符")] string operator_,
                          [ParameterDescription("第二个数值")] object value2)
        {
            try
            {
                var num1 = Convert.ToDouble(value1);
                var num2 = Convert.ToDouble(value2);

                return operator_ switch
                {
                    ">" => num1 > num2,
                    ">=" => num1 >= num2,
                    "<" => num1 < num2,
                    "<=" => num1 <= num2,
                    "==" => Math.Abs(num1 - num2) < double.Epsilon,
                    "!=" => Math.Abs(num1 - num2) >= double.Epsilon,
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 数学运算
        /// </summary>
        [UtilsFunction("数学计算", "执行基本的数学运算", "Math", "数学运算", "int")]
        [FunctionExample("加法", "Utils.MATH_CALC(10, \"+\", 5)", "15", "计算10加5")]
        [FunctionExample("除法", "Utils.MATH_CALC(20, \"/\", 4)", "5", "计算20除以4")]
        [FunctionExample("幂运算", "Utils.MATH_CALC(2, \"^\", 3)", "8", "计算2的3次方")]
        public double MATH_CALC([ParameterDescription("第一个数值")] double value1,
                              [ParameterDescription("运算操作符")] string operator_,
                              [ParameterDescription("第二个数值")] double value2)
        {
            return operator_ switch
            {
                "+" => value1 + value2,
                "-" => value1 - value2,
                "*" => value1 * value2,
                "/" => value2 != 0 ? value1 / value2 : 0,
                "%" => value2 != 0 ? value1 % value2 : 0,
                "^" => Math.Pow(value1, value2),
                _ => 0
            };
        }

        /// <summary>
        /// 数值四舍五入
        /// </summary>
        [UtilsFunction("四舍五入", "数值四舍五入到指定小数位", "Math", "数学运算", "int")]
        [FunctionExample("整数四舍五入", "Utils.MATH_ROUND(3.7)", "4", "将3.7四舍五入到整数")]
        [FunctionExample("保留小数位", "Utils.MATH_ROUND(3.14159, 2)", "3.14", "保留2位小数")]
        [FunctionExample("负数四舍五入", "Utils.MATH_ROUND(-2.6)", "-3", "负数的四舍五入")]
        public double MATH_ROUND([ParameterDescription("要四舍五入的数值")] double value,
                               [ParameterDescription("保留的小数位数")] int digits = 0)
        {
            return Math.Round(value, digits);
        }

        /// <summary>
        /// 数值向上取整
        /// </summary>
        [UtilsFunction("向上取整", "数值向上取整", "Math", "数学运算", "int")]
        [FunctionExample("正数向上取整", "Utils.MATH_CEIL(3.2)", "4", "将3.2向上取整为4")]
        [FunctionExample("负数向上取整", "Utils.MATH_CEIL(-2.8)", "-2", "将-2.8向上取整为-2")]
        [FunctionExample("整数不变", "Utils.MATH_CEIL(5)", "5", "整数向上取整保持不变")]
        public double MATH_CEIL([ParameterDescription("要向上取整的数值")] double value)
        {
            return Math.Ceiling(value);
        }

        /// <summary>
        /// 数值向下取整
        /// </summary>
        [UtilsFunction("向下取整", "数值向下取整", "Math", "数学运算", "int")]
        [FunctionExample("正数向下取整", "Utils.MATH_FLOOR(3.8)", "3", "将3.8向下取整为3")]
        [FunctionExample("负数向下取整", "Utils.MATH_FLOOR(-2.2)", "-3", "将-2.2向下取整为-3")]
        [FunctionExample("整数不变", "Utils.MATH_FLOOR(5)", "5", "整数向下取整保持不变")]
        public double MATH_FLOOR([ParameterDescription("要向下取整的数值")] double value)
        {
            return Math.Floor(value);
        }

        /// <summary>
        /// 绝对值
        /// </summary>
        [UtilsFunction("绝对值", "获取数值的绝对值", "Math", "数学运算", "int")]
        [FunctionExample("正数绝对值", "Utils.MATH_ABS(5)", "5", "正数的绝对值是它本身")]
        [FunctionExample("负数绝对值", "Utils.MATH_ABS(-5)", "5", "负数的绝对值是正数")]
        [FunctionExample("零的绝对值", "Utils.MATH_ABS(0)", "0", "零的绝对值是零")]
        public double MATH_ABS([ParameterDescription("要计算绝对值的数值")] double value)
        {
            return Math.Abs(value);
        }
        #endregion
    }
}
