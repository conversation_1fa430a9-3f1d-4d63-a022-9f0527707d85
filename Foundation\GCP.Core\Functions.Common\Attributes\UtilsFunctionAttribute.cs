using System;

namespace GCP.Functions.Common.Attributes
{
    /// <summary>
    /// Utils工具函数属性，用于定义函数的中文名称、描述和分类
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class UtilsFunctionAttribute : Attribute
    {
        /// <summary>
        /// 函数的中文显示名称
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 函数的中文描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 函数分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 分类的中文显示名称
        /// </summary>
        public string CategoryDisplayName { get; set; }

        /// <summary>
        /// 输出类型
        /// </summary>
        public string OutputType { get; set; }

        /// <summary>
        /// 使用示例（JSON格式的示例数组）
        /// </summary>
        public string Examples { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="displayName">中文显示名称</param>
        /// <param name="description">中文描述</param>
        /// <param name="category">分类</param>
        /// <param name="categoryDisplayName">分类中文名称</param>
        /// <param name="outputType">输出类型</param>
        /// <param name="examples">使用示例（JSON格式）</param>
        public UtilsFunctionAttribute(string displayName, string description, string category = "Common", string categoryDisplayName = "常用函数", string outputType = "object", string examples = null)
        {
            DisplayName = displayName;
            Description = description;
            Category = category;
            CategoryDisplayName = categoryDisplayName;
            OutputType = outputType;
            Examples = examples;
        }
    }
}
